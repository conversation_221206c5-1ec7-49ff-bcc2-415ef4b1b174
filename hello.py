
from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, APIRouter, Depends
from fastapi.middleware.cors import CORSMiddleware
import os
from routes.auth import auth_router
from routes.order import order_router
from routes.meta import myfxbook_router
from routes.account import account_router
from routes.points import points_router
from routes.email_preview import email_preview_router
from db import create_db_and_tables
import psycopg2
import os
import json
from dotenv import load_dotenv
from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Creating tables...")
    create_db_and_tables()
    print("Table created")
    try:
        yield
    finally:
        print("Lifespan context ended")
DATABASE_URL = os.getenv("DATABASE_URL")

app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust this to your needs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth_router)
app.include_router(order_router)
app.include_router(myfxbook_router)
app.include_router(account_router)
app.include_router(points_router)
app.include_router(email_preview_router)
@app.get("/fetch-data")
def fetch_data():
    if not DATABASE_URL:
        return {"error": "DATABASE_URL is not set in .env file"}

    # Connect to PostgreSQL
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor()

    # Fetch all data
    cur.execute("SELECT * FROM user")  # Replace 'your_table' with your table name
    rows = cur.fetchall()

    # Get column names
    col_names = [desc[0] for desc in cur.description]

    # Convert to list of dictionaries
    data = [dict(zip(col_names, row)) for row in rows]

    cur.close()
    conn.close()

    return json.dumps(data)
@app.get("/")
def read_root():
    return {"Welcome to ": "Multi CHATbOT CREATIOR"}



