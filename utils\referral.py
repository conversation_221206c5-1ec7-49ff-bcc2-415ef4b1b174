import secrets
import string
from sqlmodel import Session, select
from models.user import User
from models.points_transaction import PointsTransaction, TransactionType
from typing import Optional

def generate_unique_referral_code(session: Session) -> str:
    """Generate a unique 8-character referral code"""
    characters = string.ascii_uppercase + string.digits
    
    while True:
        code = ''.join(secrets.choice(characters) for _ in range(8))
        # Check if code already exists
        existing_user = session.exec(select(User).where(User.referral_code == code)).first()
        if not existing_user:
            return code

def validate_referral_code(referral_code: str, session: Session) -> Optional[User]:
    """Validate referral code and return the referring user if valid"""
    if not referral_code:
        return None
    
    referring_user = session.exec(select(User).where(User.referral_code == referral_code)).first()
    return referring_user

def award_referral_points(referring_user: User, new_user: User, session: Session, points: int = 50) -> PointsTransaction:
    """Award points to the referring user and create transaction record"""
    # Update referring user's points balance
    referring_user.points_balance += points
    session.add(referring_user)

    # Create transaction record
    transaction = PointsTransaction(
        user_id=referring_user.id,
        transaction_type=TransactionType.REFERRAL_BONUS,
        points_amount=points,
        description=f"Referral bonus for user {new_user.username} ({new_user.email})",
        referred_user_id=new_user.id
    )
    session.add(transaction)

    # Send referral bonus email
    try:
        send_referral_bonus_email(referring_user, new_user, points)
    except Exception as e:
        print(f"Error sending referral bonus email: {str(e)}")
        # Continue even if email fails

    return transaction

def send_referral_bonus_email(referring_user: User, new_user: User, points: int):
    """Send referral bonus notification email"""
    from templates.email_templates import create_referral_bonus_email
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart

    def send_email(to_email, subject, body):
        from_email = "<EMAIL>"
        from_password = "Fundedwhales@9"

        msg = MIMEMultipart()
        msg["From"] = from_email
        msg["To"] = to_email
        msg["Subject"] = subject
        msg.attach(MIMEText(body, "html"))

        with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
            server.login(from_email, from_password)
            server.sendmail(from_email, to_email, msg.as_string())

    subject = f"🎉 You Earned {points} Points! - Referral Bonus"
    html_content = create_referral_bonus_email(
        referring_user.username,
        new_user.username,
        new_user.email,
        points
    )

    send_email(referring_user.email, subject, html_content)

def add_points_to_user(user: User, points: int, transaction_type: TransactionType, 
                      description: str, session: Session, reference_id: Optional[str] = None) -> PointsTransaction:
    """Add points to a user's account and create transaction record"""
    # Update user's points balance
    user.points_balance += points
    session.add(user)
    
    # Create transaction record
    transaction = PointsTransaction(
        user_id=user.id,
        transaction_type=transaction_type,
        points_amount=points,
        description=description,
        reference_id=reference_id
    )
    session.add(transaction)
    
    return transaction

def deduct_points_from_user(user: User, points: int, transaction_type: TransactionType,
                           description: str, session: Session, reference_id: Optional[str] = None) -> Optional[PointsTransaction]:
    """Deduct points from a user's account if they have sufficient balance"""
    if user.points_balance < points:
        return None  # Insufficient balance
    
    # Update user's points balance
    user.points_balance -= points
    session.add(user)
    
    # Create transaction record (negative amount for deduction)
    transaction = PointsTransaction(
        user_id=user.id,
        transaction_type=transaction_type,
        points_amount=-points,
        description=description,
        reference_id=reference_id
    )
    session.add(transaction)
    
    return transaction
