from datetime import datetime

def create_base_email_template(title, content, button_text=None, button_url=None, accent_color="#FF6B35"):
    """
    Modern professional email template with vibrant color scheme
    Colors: Orange (#FF6B35), <PERSON> Blue (#1A365D), <PERSON> (#38A169)
    """
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>{title}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

            /* Reset styles */
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}

            /* Responsive styles */
            @media only screen and (max-width: 600px) {{
                .email-container {{ width: 100% !important; margin: 0 !important; }}
                .email-content {{ padding: 20px !important; }}
                .email-header {{ padding: 25px 20px !important; }}
                .email-footer {{ padding: 15px 20px !important; }}
                .button {{ padding: 12px 25px !important; font-size: 14px !important; }}
            }}
        </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
        <!-- Email Wrapper -->
        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
            <tr>
                <td style="padding: 40px 20px;">
                    <!-- Main Email Container -->
                    <table class="email-container" role="presentation" cellpadding="0" cellspacing="0" width="600" style="margin: 0 auto; border-collapse: collapse; background: #FFFFFF; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.15); overflow: hidden;">

                        <!-- Header with Logo and Branding -->
                        <tr>
                            <td class="email-header" style="background: linear-gradient(135deg, {accent_color} 0%, #E53E3E 100%); padding: 40px 50px; text-align: center; position: relative;">
                                <!-- Decorative elements -->
                                <div style="position: absolute; top: -50px; left: -50px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                <div style="position: absolute; bottom: -30px; right: -30px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.4;"></div>

                                <!-- Logo and Brand -->
                                <div style="position: relative; z-index: 2;">
                                    <h1 style="color: #FFFFFF; margin: 0 0 8px 0; font-size: 32px; font-weight: 800; letter-spacing: -0.5px; text-shadow: 0 2px 8px rgba(0,0,0,0.2);">FundedWhales</h1>
                                    <p style="color: rgba(255,255,255,0.9); margin: 0; font-size: 16px; font-weight: 500; letter-spacing: 0.5px;">PROFESSIONAL PROP TRADING</p>
                                    <div style="width: 60px; height: 3px; background: rgba(255,255,255,0.8); margin: 15px auto 0; border-radius: 2px;"></div>
                                </div>
                            </td>
                        </tr>

                        <!-- Title Section -->
                        <tr>
                            <td style="padding: 40px 50px 20px; text-align: center; background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);">
                                <h2 style="color: #1A365D; margin: 0; font-size: 28px; font-weight: 700; line-height: 1.3; letter-spacing: -0.3px;">{title}</h2>
                                <div style="width: 40px; height: 2px; background: {accent_color}; margin: 20px auto 0; border-radius: 1px;"></div>
                            </td>
                        </tr>

                        <!-- Main Content -->
                        <tr>
                            <td class="email-content" style="padding: 20px 50px 40px; background: #FFFFFF; color: #2D3748; font-size: 16px; line-height: 1.6;">
                                {content}
                            </td>
                        </tr>

                        <!-- Call-to-Action Button -->
                        {"" if not button_text else f'''
                        <tr>
                            <td style="padding: 0 50px 40px; text-align: center; background: #FFFFFF;">
                                <table role="presentation" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                    <tr>
                                        <td style="border-radius: 12px; background: linear-gradient(135deg, {accent_color} 0%, #E53E3E 100%); box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);">
                                            <a href="{button_url}" class="button" style="display: inline-block; color: #FFFFFF; text-decoration: none; padding: 16px 32px; font-weight: 600; font-size: 16px; letter-spacing: 0.3px; transition: all 0.3s ease; border-radius: 12px;">{button_text}</a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        '''}

                        <!-- Footer -->
                        <tr>
                            <td class="email-footer" style="background: #1A365D; padding: 30px 50px; text-align: center; color: #A0AEC0;">
                                <div style="margin-bottom: 20px;">
                                    <h3 style="color: #FFFFFF; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">FundedWhales</h3>
                                    <p style="margin: 0; font-size: 14px; opacity: 0.8;">Empowering Traders Worldwide</p>
                                </div>

                                <div style="border-top: 1px solid rgba(160, 174, 192, 0.2); padding-top: 20px;">
                                    <p style="margin: 0 0 10px 0; font-size: 14px;">© {datetime.now().year} FundedWhales Prop Trading. All rights reserved.</p>
                                    <p style="margin: 0; font-size: 12px; opacity: 0.7;">This email was sent to you because you have an account with FundedWhales.</p>
                                </div>

                                <!-- Social Links (Optional) -->
                                <div style="margin-top: 20px;">
                                    <a href="#" style="display: inline-block; margin: 0 10px; color: {accent_color}; text-decoration: none; font-size: 12px; font-weight: 500;">Website</a>
                                    <a href="#" style="display: inline-block; margin: 0 10px; color: {accent_color}; text-decoration: none; font-size: 12px; font-weight: 500;">Support</a>
                                    <a href="#" style="display: inline-block; margin: 0 10px; color: {accent_color}; text-decoration: none; font-size: 12px; font-weight: 500;">Contact</a>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    return html_template

def create_welcome_email(username, referral_code=None):
    """
    Modern welcome email template with referral information
    """
    referral_section = ""
    if referral_code:
        referral_section = f"""
        <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
            <div style="display: inline-block; background: #38A169; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin-bottom: 15px; font-size: 24px;">🎁</div>
            <h3 style="color: #38A169; margin: 0 0 10px 0; font-size: 20px; font-weight: 700;">Your Referral Code</h3>
            <div style="background: #FFFFFF; border: 2px dashed #38A169; border-radius: 8px; padding: 15px; margin: 15px 0;">
                <code style="font-size: 24px; font-weight: 800; color: #1A365D; letter-spacing: 2px;">{referral_code}</code>
            </div>
            <p style="margin: 10px 0 0 0; color: #2D3748; font-size: 14px;">Share this code and earn <strong>50 points</strong> for each successful referral!</p>
        </div>
        """

    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #FF6B35 0%, #F56500 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🚀</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Welcome aboard, {username}!</h3>
    </div>

    <p style="margin: 0 0 25px; font-size: 18px; color: #2D3748; text-align: center;">You've just joined the most <strong style="color: #FF6B35;">exclusive trading community</strong> in the world.</p>

    {referral_section}

    <div style="background: #F8FAFC; border-radius: 12px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">🎯 What's Next?</h4>
        <div style="display: grid; gap: 15px;">
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #FF6B35;">
                <div style="background: #FF6B35; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">1</div>
                <div>
                    <strong style="color: #1A365D;">Complete Your Profile</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Add your trading experience and preferences</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #38A169;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">2</div>
                <div>
                    <strong style="color: #1A365D;">Choose Your Challenge</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Select from $10K to $200K funding options</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #805AD5;">
                <div style="background: #805AD5; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">3</div>
                <div>
                    <strong style="color: #1A365D;">Start Trading</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Begin your journey to funded trading success</p>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Ready to unlock your trading potential?</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Let's get started! 💪</p>
    </div>
    """
    return create_base_email_template(
        "Welcome to FundedWhales! 🎉",
        content,
        "Access Your Dashboard",
        "https://fundedwhales.com/dashboard",
        "#FF6B35"
    )

def create_referral_bonus_email(username, referred_username, referred_email, points_earned=50):
    """
    Referral bonus notification email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🎉</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Congratulations, {username}!</h3>
    </div>

    <p style="margin: 0 0 25px; font-size: 18px; color: #2D3748; text-align: center;">You've earned <strong style="color: #38A169;">{points_earned} points</strong> for a successful referral!</p>

    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center; position: relative;">
        <div style="position: absolute; top: -10px; right: 20px; background: #38A169; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600;">NEW REFERRAL</div>

        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
            <div style="background: #FFFFFF; border: 2px solid #38A169; border-radius: 50%; width: 60px; height: 60px; line-height: 60px; text-align: center; margin-right: 15px; font-size: 24px;">👤</div>
            <div style="text-align: left;">
                <h4 style="color: #1A365D; margin: 0; font-size: 18px; font-weight: 600;">{referred_username}</h4>
                <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">{referred_email}</p>
            </div>
        </div>

        <div style="background: #FFFFFF; border: 2px dashed #38A169; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <div style="display: flex; align-items: center; justify-content: center;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; text-align: center; margin-right: 15px; font-size: 20px;">+</div>
                <div>
                    <h3 style="color: #38A169; margin: 0; font-size: 28px; font-weight: 800;">{points_earned} Points</h3>
                    <p style="margin: 0; color: #4A5568; font-size: 14px;">Added to your account</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #F8FAFC; border-radius: 12px; padding: 25px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 15px 0; font-size: 18px; font-weight: 600; text-align: center;">💡 Keep Earning More!</h4>
        <p style="margin: 0 0 15px 0; color: #4A5568; text-align: center;">Share your referral code with more friends and earn 50 points for each successful signup!</p>
        <div style="text-align: center;">
            <div style="background: #FFFFFF; border: 2px solid #FF6B35; border-radius: 8px; padding: 15px; display: inline-block; margin: 10px 0;">
                <p style="margin: 0 0 5px 0; color: #4A5568; font-size: 12px; font-weight: 600;">YOUR REFERRAL CODE</p>
                <code style="font-size: 20px; font-weight: 800; color: #FF6B35; letter-spacing: 2px;">SHARE_ME</code>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Thank you for helping FundedWhales grow!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Happy Trading! 🚀</p>
    </div>
    """
    return create_base_email_template(
        "You Earned Referral Points! 🎁",
        content,
        "View Your Points",
        "https://fundedwhales.com/dashboard/points",
        "#38A169"
    )

def create_points_milestone_email(username, total_points, milestone):
    """
    Points milestone achievement email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #805AD5 0%, #6B46C1 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🏆</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Milestone Achieved!</h3>
    </div>

    <p style="margin: 0 0 25px; font-size: 18px; color: #2D3748; text-align: center;">Congratulations <strong>{username}</strong>! You've reached <strong style="color: #805AD5;">{milestone} points</strong>!</p>

    <div style="background: linear-gradient(135deg, #FAF5FF 0%, #E9D8FD 100%); border: 2px solid #805AD5; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center;">
        <div style="background: #FFFFFF; border: 2px solid #805AD5; border-radius: 12px; padding: 25px; margin: 20px 0;">
            <h2 style="color: #805AD5; margin: 0; font-size: 36px; font-weight: 800;">{total_points}</h2>
            <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 16px; font-weight: 600;">Total Points Earned</p>
        </div>

        <div style="margin: 20px 0;">
            <p style="margin: 0; color: #1A365D; font-size: 16px; font-weight: 600;">🎯 Next Milestone: {milestone + 100} Points</p>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Keep earning points through referrals and activities!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">You're doing amazing! 🌟</p>
    </div>
    """
    return create_base_email_template(
        f"🏆 {milestone} Points Milestone Reached!",
        content,
        "View Your Dashboard",
        "https://fundedwhales.com/dashboard/points",
        "#805AD5"
    )

def create_order_confirmation_email(username, order_id, challenge_type, account_size, platform):
    """
    Modern order confirmation email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">✅</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Order Confirmed!</h3>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Thank you <strong>{username}</strong>! Your trading challenge order has been <strong style="color: #38A169;">successfully processed</strong>.</p>

    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 25px 0; font-size: 20px; font-weight: 600; text-align: center;">📋 Order Details</h4>

        <div style="background: #FFFFFF; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
            <div style="display: grid; gap: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Order Number:</span>
                    <span style="color: #1A365D; font-weight: 700; font-family: monospace;">FxE{order_id}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Challenge Type:</span>
                    <span style="color: #FF6B35; font-weight: 700;">{challenge_type}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Account Size:</span>
                    <span style="color: #1A365D; font-weight: 700; font-size: 18px;">{account_size}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                    <span style="color: #4A5568; font-weight: 500;">Platform:</span>
                    <span style="color: #1A365D; font-weight: 700;">{platform}</span>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #FFF5F5; border: 1px solid #FEB2B2; border-radius: 12px; padding: 25px; margin: 30px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="background: #E53E3E; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 16px;">⏱</div>
            <h4 style="color: #C53030; margin: 0; font-size: 18px; font-weight: 600;">What Happens Next?</h4>
        </div>
        <div style="margin-left: 45px;">
            <p style="margin: 0 0 10px 0; color: #4A5568; font-size: 15px;">• Our team is preparing your trading challenge account</p>
            <p style="margin: 0 0 10px 0; color: #4A5568; font-size: 15px;">• You'll receive login credentials within 24 hours</p>
            <p style="margin: 0; color: #4A5568; font-size: 15px;">• Check your email for account setup notification</p>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Questions about your order?</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Our support team is here to help! 🚀</p>
    </div>
    """
    return create_base_email_template(
        "Order Confirmation - FundedWhales",
        content,
        "Track Your Order",
        f"https://fundedwhales.com/orders/{order_id}",
        "#38A169"
    )

def create_challenge_completion_email(username, order_id, server, login, password):
    """
    Challenge completion email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">Your trading challenge account has been successfully set up and is ready for trading!</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Your Trading Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Please make sure to read our trading rules and risk management guidelines before starting your challenge.</p>
    """
    return create_base_email_template(
        "Your Challenge Account is Ready",
        content,
        "Start Trading Now",
        f"https://fundedwhales.com/dashboard/challenge/{order_id}"
    )

def create_pass_notification_email(username, order_id, profit_amount=None):
    """
    Challenge pass notification email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <div style="text-align: center; margin: 30px 0;">
        <div style="display: inline-block; background: #F0FFF4; border: 2px solid #48BB78; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 20px;">
            <span style="color: #48BB78; font-size: 40px;">✓</span>
        </div>
        <h2 style="color: #48BB78; margin: 0;">Congratulations!</h2>
    </div>
    
    <p style="margin: 0 0 20px; text-align: center; font-size: 18px;">You've successfully passed your trading challenge!</p>
    
    {f'<p style="margin: 20px 0; text-align: center; color: #2C5282; font-weight: 600;">Total Profit: ${profit_amount}</p>' if profit_amount else ''}
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Next Steps:</h4>
        <ol style="margin: 0; padding-left: 20px; color: #4A5568;">
            <li style="margin-bottom: 10px;">Our team will review your trading performance</li>
            <li style="margin-bottom: 10px;">We'll prepare your funded account credentials</li>
            <li>You'll receive another email with your funded account details</li>
        </ol>
    </div>
    """
    return create_base_email_template(
        "Challenge Passed Successfully!",
        content,
        "View Performance",
        f"https://fundedwhales.com/dashboard/challenge/{order_id}/results"
    )

def create_fail_notification_email(username, order_id, reason):
    """
    Challenge fail notification email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">We regret to inform you that your trading challenge has been marked as failed.</p>
    
    <div style="background: #FFF5F5; border: 1px solid #FC8181; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #C53030; margin: 0 0 15px;">Reason for Failure:</h4>
        <p style="margin: 0; color: #4A5568;">{reason}</p>
    </div>
    
    <p style="margin: 20px 0;">Don't give up! Many successful traders faced setbacks before achieving their goals. You can always try again with a new challenge.</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">What's Next?</h4>
        <ul style="margin: 0; padding-left: 20px; color: #4A5568;">
            <li style="margin-bottom: 10px;">Review your trading performance and learn from this experience</li>
            <li style="margin-bottom: 10px;">Consider our educational resources to improve your strategy</li>
            <li>Start a new challenge when you're ready</li>
        </ul>
    </div>
    """
    return create_base_email_template(
        "Challenge Status Update",
        content,
        "Try Again",
        "https://fundedwhales.com/challenges"
    )

def create_live_account_email(username, order_id, server, login, password, profit_share):
    """
    Live account creation email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <div style="text-align: center; margin: 30px 0;">
        <div style="display: inline-block; background: #F0FFF4; border: 2px solid #48BB78; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 20px;">
            <span style="color: #48BB78; font-size: 40px;">🌟</span>
        </div>
        <h2 style="color: #48BB78; margin: 0;">Welcome to the Funded Traders Club!</h2>
    </div>
    
    <p style="margin: 0 0 20px;">Your live trading account has been successfully created. Here are your account details:</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Live Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Profit Share:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{profit_share}%</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Remember to follow our trading rules and risk management guidelines. Happy trading!</p>
    """
    return create_base_email_template(
        "Your Live Account is Ready",
        content,
        "Start Live Trading",
        f"https://fundedwhales.com/dashboard/live/{order_id}"
    )

def create_certificate_email(username, order_id, challenge_type, account_size, certificate_id, issue_date, profit_amount=None):
    """
    Advanced certificate email template with luxury design
    """
    content = f"""
    <div style="background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%); border-radius: 15px; padding: 3px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%); border-radius: 12px; padding: 40px; position: relative; overflow: hidden;">
            <!-- Decorative Elements -->
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNMCAwbDUwIDUwTDAgMTAwIiBzdHJva2U9InJnYmEoNzQsIDE0NCwgMjI2LCAwLjEpIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=') repeat; opacity: 0.1;"></div>
            
            <!-- Certificate Header -->
            <div style="text-align: center; position: relative; z-index: 1;">
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #4A90E2; margin: 0; font-size: 36px; letter-spacing: 3px; text-transform: uppercase; font-weight: 800; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">Certificate of Achievement</h1>
                    <div style="width: 100px; height: 3px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 15px auto;"></div>
                    <p style="color: #2d3748; font-size: 18px; margin: 10px 0;">FundedWhales Trading Excellence</p>
                </div>
                
                <!-- Recipient Info -->
                <div style="margin: 40px 0;">
                    <p style="color: #4a5568; font-size: 16px; margin: 0;">This certificate is proudly presented to</p>
                    <h2 style="color: #2d3748; font-size: 32px; margin: 15px 0; font-family: 'Playfair Display', serif;">{username}</h2>
                    <p style="color: #4a5568; font-size: 16px; line-height: 1.6; max-width: 600px; margin: 20px auto;">
                        For demonstrating exceptional trading proficiency and successfully completing the FundedWhales Trading Challenge
                        with outstanding performance and adherence to risk management principles.
                    </p>
                </div>
                
                <!-- Achievement Details -->
                <div style="background: linear-gradient(135deg, #EBF8FF 0%, #F0F7FF 100%); border: 1px solid rgba(74, 144, 226, 0.2); border-radius: 12px; padding: 25px; margin: 30px auto; max-width: 500px; box-shadow: 0 4px 6px rgba(74, 144, 226, 0.1);">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Challenge Type:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{challenge_type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Account Size:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{account_size}</td>
                        </tr>
                        {f'''
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Total Profit:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #48BB78; font-weight: 600;">${profit_amount}</td>
                        </tr>
                        ''' if profit_amount else ''}
                        <tr>
                            <td style="padding: 12px; color: #4a5568;">Certificate ID:</td>
                            <td style="padding: 12px; color: #2d3748; font-weight: 600;">{certificate_id}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Signatures -->
                <div style="display: flex; justify-content: space-between; margin-top: 40px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Dancing Script', cursive; color: #2d3748; font-size: 28px;">Maxwell Grant</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Chief Executive Officer</p>
                    </div>
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Montserrat', sans-serif; color: #2d3748; font-size: 18px;">{issue_date}</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Date of Issue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """
    return create_base_email_template(
        "Trading Excellence Certificate",
        content,
        "View Certificate",
        f"https://fundedwhales.com/certificates/{certificate_id}"
    ) 