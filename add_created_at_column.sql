-- Check if the column already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='ordermodel' AND column_name='created_at'
    ) THEN
        -- Add the created_at column with a default value of current timestamp
        ALTER TABLE ordermodel 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        
        RAISE NOTICE 'Column created_at added to ordermodel table';
    ELSE
        RAISE NOTICE 'Column created_at already exists in ordermodel table';
    END IF;
END $$;
