from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from models.user import User
from models.giveaway import Giveaway, GiveawayEntry, GiveawayWinner, GiveawayStatus, GiveawayEntryStatus
from auth import get_current_user
from db import get_session
from templates.email_templates import create_giveaway_entry_email, create_giveaway_winner_email
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

giveaway_router = APIRouter(prefix="/giveaways", tags=["Giveaways"])

# Response models
class GiveawayResponse(BaseModel):
    id: int
    title: str
    description: str
    prize_description: str
    prize_value: float
    min_account_size: float
    start_date: datetime
    end_date: datetime
    status: str
    total_entries: int
    max_entries: Optional[int]
    winner_id: Optional[int]
    winner_selected_at: Optional[datetime]

class GiveawayEntryResponse(BaseModel):
    id: int
    giveaway_id: int
    user_id: int
    order_id: int
    account_size: float
    entry_date: datetime
    status: str

class UserGiveawayEntryResponse(BaseModel):
    id: int
    giveaway: GiveawayResponse
    account_size: float
    entry_date: datetime
    status: str

# Request models
class CreateGiveawayRequest(BaseModel):
    title: str
    description: str
    prize_description: str
    prize_value: float
    min_account_size: float = 50000.0
    start_date: datetime
    end_date: datetime
    max_entries: Optional[int] = None
    terms_and_conditions: Optional[str] = None

# Email sending function
def send_email(to_email, subject, body):
    from_email = "<EMAIL>"
    from_password = "Fundedwhales@9"

    msg = MIMEMultipart()
    msg["From"] = from_email
    msg["To"] = to_email
    msg["Subject"] = subject
    msg.attach(MIMEText(body, "html"))

    with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
        server.login(from_email, from_password)
        server.sendmail(from_email, to_email, msg.as_string())

# Get all active giveaways
@giveaway_router.get("/", response_model=List[GiveawayResponse])
def get_active_giveaways(session: Session = Depends(get_session)):
    """Get all active giveaways"""
    giveaways = session.exec(
        select(Giveaway)
        .where(Giveaway.status == GiveawayStatus.ACTIVE)
        .where(Giveaway.end_date > datetime.utcnow())
    ).all()
    
    return [
        GiveawayResponse(
            id=g.id,
            title=g.title,
            description=g.description,
            prize_description=g.prize_description,
            prize_value=g.prize_value,
            min_account_size=g.min_account_size,
            start_date=g.start_date,
            end_date=g.end_date,
            status=g.status,
            total_entries=g.total_entries,
            max_entries=g.max_entries,
            winner_id=g.winner_id,
            winner_selected_at=g.winner_selected_at
        )
        for g in giveaways
    ]

# Get user's giveaway entries
@giveaway_router.get("/my-entries", response_model=List[UserGiveawayEntryResponse])
def get_user_giveaway_entries(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """Get current user's giveaway entries"""
    entries = session.exec(
        select(GiveawayEntry, Giveaway)
        .join(Giveaway)
        .where(GiveawayEntry.user_id == current_user.id)
        .order_by(GiveawayEntry.entry_date.desc())
    ).all()
    
    return [
        UserGiveawayEntryResponse(
            id=entry.id,
            giveaway=GiveawayResponse(
                id=giveaway.id,
                title=giveaway.title,
                description=giveaway.description,
                prize_description=giveaway.prize_description,
                prize_value=giveaway.prize_value,
                min_account_size=giveaway.min_account_size,
                start_date=giveaway.start_date,
                end_date=giveaway.end_date,
                status=giveaway.status,
                total_entries=giveaway.total_entries,
                max_entries=giveaway.max_entries,
                winner_id=giveaway.winner_id,
                winner_selected_at=giveaway.winner_selected_at
            ),
            account_size=entry.account_size,
            entry_date=entry.entry_date,
            status=entry.status
        )
        for entry, giveaway in entries
    ]

# Get giveaway details
@giveaway_router.get("/{giveaway_id}", response_model=GiveawayResponse)
def get_giveaway_details(giveaway_id: int, session: Session = Depends(get_session)):
    """Get details of a specific giveaway"""
    giveaway = session.get(Giveaway, giveaway_id)
    if not giveaway:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Giveaway not found"
        )
    
    return GiveawayResponse(
        id=giveaway.id,
        title=giveaway.title,
        description=giveaway.description,
        prize_description=giveaway.prize_description,
        prize_value=giveaway.prize_value,
        min_account_size=giveaway.min_account_size,
        start_date=giveaway.start_date,
        end_date=giveaway.end_date,
        status=giveaway.status,
        total_entries=giveaway.total_entries,
        max_entries=giveaway.max_entries,
        winner_id=giveaway.winner_id,
        winner_selected_at=giveaway.winner_selected_at
    )

# Get giveaway entries (for a specific giveaway)
@giveaway_router.get("/{giveaway_id}/entries", response_model=List[GiveawayEntryResponse])
def get_giveaway_entries(
    giveaway_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get entries for a specific giveaway (admin only for now)"""
    # Note: Add proper admin authentication here
    
    giveaway = session.get(Giveaway, giveaway_id)
    if not giveaway:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Giveaway not found"
        )
    
    entries = session.exec(
        select(GiveawayEntry)
        .where(GiveawayEntry.giveaway_id == giveaway_id)
        .order_by(GiveawayEntry.entry_date.desc())
    ).all()
    
    return [
        GiveawayEntryResponse(
            id=entry.id,
            giveaway_id=entry.giveaway_id,
            user_id=entry.user_id,
            order_id=entry.order_id,
            account_size=entry.account_size,
            entry_date=entry.entry_date,
            status=entry.status
        )
        for entry in entries
    ]

# Function to automatically enter user in giveaway (called from order system)
def auto_enter_giveaway(user: User, order_id: int, account_size: float, session: Session):
    """Automatically enter user in active giveaways if they qualify"""
    # Convert account size string to float if needed
    if isinstance(account_size, str):
        # Remove $ and , from account size string
        account_size_clean = account_size.replace('$', '').replace(',', '')
        try:
            account_size = float(account_size_clean)
        except ValueError:
            print(f"Could not parse account size: {account_size}")
            return
    
    # Find active giveaways that the user qualifies for
    active_giveaways = session.exec(
        select(Giveaway)
        .where(Giveaway.status == GiveawayStatus.ACTIVE)
        .where(Giveaway.start_date <= datetime.utcnow())
        .where(Giveaway.end_date > datetime.utcnow())
        .where(Giveaway.min_account_size <= account_size)
    ).all()
    
    for giveaway in active_giveaways:
        # Check if user is already entered in this giveaway
        existing_entry = session.exec(
            select(GiveawayEntry)
            .where(GiveawayEntry.giveaway_id == giveaway.id)
            .where(GiveawayEntry.user_id == user.id)
        ).first()
        
        if not existing_entry:
            # Check if giveaway has reached max entries
            if giveaway.max_entries and giveaway.total_entries >= giveaway.max_entries:
                continue
            
            # Create new entry
            entry = GiveawayEntry(
                giveaway_id=giveaway.id,
                user_id=user.id,
                order_id=order_id,
                account_size=account_size,
                status=GiveawayEntryStatus.ACTIVE
            )
            session.add(entry)
            
            # Update giveaway total entries
            giveaway.total_entries += 1
            session.add(giveaway)
            
            try:
                session.commit()
                session.refresh(entry)
                
                # Send giveaway entry email
                try:
                    send_giveaway_entry_email(user, giveaway, account_size)
                    print(f"Sent giveaway entry email to {user.email}")
                except Exception as e:
                    print(f"Error sending giveaway entry email: {str(e)}")
                
            except Exception as e:
                session.rollback()
                print(f"Error creating giveaway entry: {str(e)}")

def send_giveaway_entry_email(user: User, giveaway: Giveaway, account_size: float):
    """Send giveaway entry notification email"""
    subject = f"🎁 You're Entered! {giveaway.prize_description} Giveaway"
    html_content = create_giveaway_entry_email(
        user.username,
        giveaway.title,
        giveaway.prize_description,
        account_size,
        giveaway.end_date.strftime("%B %d, %Y")
    )
    
    send_email(user.email, subject, html_content)

# Admin endpoints
@giveaway_router.post("/admin/create", response_model=GiveawayResponse)
def create_giveaway(
    request: CreateGiveawayRequest,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Create a new giveaway (admin only)"""
    # Note: Add proper admin authentication here

    giveaway = Giveaway(
        title=request.title,
        description=request.description,
        prize_description=request.prize_description,
        prize_value=request.prize_value,
        min_account_size=request.min_account_size,
        start_date=request.start_date,
        end_date=request.end_date,
        max_entries=request.max_entries,
        terms_and_conditions=request.terms_and_conditions,
        status=GiveawayStatus.ACTIVE
    )

    session.add(giveaway)
    session.commit()
    session.refresh(giveaway)

    return GiveawayResponse(
        id=giveaway.id,
        title=giveaway.title,
        description=giveaway.description,
        prize_description=giveaway.prize_description,
        prize_value=giveaway.prize_value,
        min_account_size=giveaway.min_account_size,
        start_date=giveaway.start_date,
        end_date=giveaway.end_date,
        status=giveaway.status,
        total_entries=giveaway.total_entries,
        max_entries=giveaway.max_entries,
        winner_id=giveaway.winner_id,
        winner_selected_at=giveaway.winner_selected_at
    )

@giveaway_router.post("/admin/{giveaway_id}/select-winner")
def select_giveaway_winner(
    giveaway_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Randomly select a winner for the giveaway (admin only)"""
    import random

    # Note: Add proper admin authentication here

    giveaway = session.get(Giveaway, giveaway_id)
    if not giveaway:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Giveaway not found"
        )

    if giveaway.winner_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Winner already selected for this giveaway"
        )

    # Get all active entries
    entries = session.exec(
        select(GiveawayEntry)
        .where(GiveawayEntry.giveaway_id == giveaway_id)
        .where(GiveawayEntry.status == GiveawayEntryStatus.ACTIVE)
    ).all()

    if not entries:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No eligible entries found for this giveaway"
        )

    # Randomly select winner
    winning_entry = random.choice(entries)
    winner = session.get(User, winning_entry.user_id)

    # Update giveaway with winner
    giveaway.winner_id = winner.id
    giveaway.winner_selected_at = datetime.utcnow()
    giveaway.status = GiveawayStatus.ENDED

    # Update winning entry status
    winning_entry.status = GiveawayEntryStatus.WINNER

    # Create winner record
    winner_record = GiveawayWinner(
        giveaway_id=giveaway.id,
        user_id=winner.id,
        entry_id=winning_entry.id
    )

    session.add(giveaway)
    session.add(winning_entry)
    session.add(winner_record)
    session.commit()

    # Send winner notification email
    try:
        send_winner_notification_email(winner, giveaway)
        winner_record.notified_at = datetime.utcnow()
        session.add(winner_record)
        session.commit()
    except Exception as e:
        print(f"Error sending winner notification email: {str(e)}")

    return {
        "message": f"Winner selected successfully!",
        "winner_username": winner.username,
        "winner_email": winner.email,
        "giveaway_title": giveaway.title,
        "prize": giveaway.prize_description
    }

def send_winner_notification_email(winner: User, giveaway: Giveaway):
    """Send winner notification email"""
    subject = f"🏆 WINNER! You Won Our {giveaway.prize_description} Giveaway!"
    html_content = create_giveaway_winner_email(
        winner.username,
        giveaway.title,
        giveaway.prize_description
    )

    send_email(winner.email, subject, html_content)
