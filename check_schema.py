import os
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

print(f"Database URL exists: {database_url is not None}")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

try:
    # Connect to the database
    print("Attempting to connect to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    cur = conn.cursor()

    # Query to get column information for completeordermodel table
    print("Executing query...")
    cur.execute("""
        SELECT column_name, is_nullable, data_type, column_default
        FROM information_schema.columns
        WHERE table_name='completeordermodel'
        ORDER BY ordinal_position
    """)

    # Print column information
    print("CompleteOrderModel Table Schema:")
    print("--------------------------------")
    rows = cur.fetchall()
    print(f"Found {len(rows)} columns")

    for row in rows:
        print(f"Column: {row[0]}")
        print(f"  Nullable: {row[1]}")
        print(f"  Data Type: {row[2]}")
        print(f"  Default: {row[3]}")
        print()

    cur.close()
    conn.close()

except Exception as e:
    print(f"Error: {str(e)}")
