"""
Test script to simulate order creation and giveaway entry
This will help us test the exact flow that happens when a user creates an order
"""

import requests
import json

# Base URL for your API
BASE_URL = "http://localhost:8000"

def test_order_creation_with_giveaway():
    """Test creating an order that should trigger giveaway entry"""
    print("🛒 Testing Order Creation with Giveaway Entry")
    print("=" * 50)
    
    # First, you need to login to get a token
    # Replace these with actual user credentials
    login_data = {
        "username": "<EMAIL>",  # Replace with your email
        "password": "your_password"  # Replace with your password
    }
    
    print("1. Attempting to login...")
    login_response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print("Please update the credentials in this script with valid user credentials")
        print("Or create a new user account first")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Test different account sizes
    test_cases = [
        {
            "account_size": "$50,000",
            "should_qualify": True,
            "description": "Minimum qualifying amount"
        },
        {
            "account_size": "$75,000", 
            "should_qualify": True,
            "description": "Above minimum amount"
        },
        {
            "account_size": "$25,000",
            "should_qualify": False,
            "description": "Below minimum amount"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing with {test_case['account_size']} ({test_case['description']})")
        print("-" * 40)
        
        # Create order data
        order_data = {
            "email": login_data["username"],
            "challenge_type": "Phase 1 Challenge",
            "account_size": test_case["account_size"],
            "platform": "MetaTrader 5",
            "payment_method": "Cryptocurrency",
            "txid": f"test_giveaway_{i}_{hash(test_case['account_size'])}"
        }
        
        print(f"   Creating order with account size: {test_case['account_size']}")
        
        # Create the order
        response = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
        
        print(f"   Order creation status: {response.status_code}")
        
        if response.status_code == 200:
            order = response.json()
            print(f"   ✅ Order created successfully (ID: {order.get('id', 'N/A')})")
            
            if test_case["should_qualify"]:
                print(f"   🎁 This order should trigger giveaway entry")
                print(f"   📧 User should receive giveaway entry email")
            else:
                print(f"   ❌ This order should NOT trigger giveaway entry (below minimum)")
                
        else:
            print(f"   ❌ Order creation failed: {response.json()}")
    
    # Check user's giveaway entries
    print(f"\n4. Checking user's giveaway entries...")
    entries_response = requests.get(f"{BASE_URL}/giveaways/my-entries", headers=headers)
    
    if entries_response.status_code == 200:
        entries = entries_response.json()
        print(f"   ✅ User has {len(entries)} giveaway entries:")
        for entry in entries:
            print(f"      - Giveaway: {entry['giveaway']['title']}")
            print(f"        Account Size: ${entry['account_size']:,.0f}")
            print(f"        Entry Date: {entry['entry_date']}")
            print(f"        Status: {entry['status']}")
    else:
        print(f"   ❌ Failed to get giveaway entries: {entries_response.json()}")
    
    return True

def check_server_logs():
    """Instructions for checking server logs"""
    print(f"\n📋 To see what's happening:")
    print("1. Check your FastAPI server console for debug messages")
    print("2. Look for messages starting with '🎁 ORDER DEBUG:' and '🎁 GIVEAWAY DEBUG:'")
    print("3. If you don't see these messages, the giveaway code might not be reached")
    print("4. If you see the messages but no email, check email server configuration")

def main():
    """Main test function"""
    print("🧪 Order + Giveaway Integration Test")
    print("=" * 50)
    
    print("⚠️  IMPORTANT: Update the login credentials in this script before running!")
    print("   Edit the 'login_data' dictionary with valid user credentials")
    print()
    
    # Uncomment the line below after updating credentials
    # test_order_creation_with_giveaway()
    
    check_server_logs()
    
    print("\n" + "=" * 50)
    print("🔍 Test Instructions:")
    print("1. Update login credentials in this script")
    print("2. Make sure your FastAPI server is running")
    print("3. Run this script to test order creation")
    print("4. Check server console for debug messages")
    print("5. Check your email for giveaway notifications")

if __name__ == "__main__":
    main()
