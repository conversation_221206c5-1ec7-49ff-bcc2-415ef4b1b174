import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def run_migration():
    """
    Run the SQL migration script to add the created_at column to the ordermodel table
    """
    print("Starting migration to add created_at column to ordermodel table...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    
    try:
        # Read the SQL migration script
        with open('add_created_at_column.sql', 'r') as f:
            sql = f.read()
        
        # Execute the SQL migration script
        cursor.execute(sql)
        
        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    run_migration()
