import os
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def migrate_add_created_at():
    """
    Migration script to add created_at column to ordermodel table
    """
    print("Starting migration to add created_at column to ordermodel table...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    
    try:
        # Check if the column already exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='ordermodel' AND column_name='created_at';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'created_at' does not exist. Adding it...")
            
            # Add the created_at column with a default value of current timestamp
            cursor.execute("""
                ALTER TABLE ordermodel 
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """)
            
            print("Column 'created_at' added successfully.")
        else:
            print("Column 'created_at' already exists. No changes needed.")
        
        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    migrate_add_created_at()
