import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL not found in .env file, checking environment variables...")
    database_url = os.environ.get("DATABASE_URL")
    
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database...")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Create SQLAlchemy engine
engine = create_engine(database_url)
print("Database connection established successfully.")

# Check if account_credentials table exists
with engine.connect() as conn:
    result = conn.execute(text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'account_credentials'
        );
    """))
    
    table_exists = result.scalar()
    
    if not table_exists:
        print("Creating account_credentials table...")
        
        # Create the table with all required columns
        conn.execute(text("""
            CREATE TABLE account_credentials (
                id SERIAL PRIMARY KEY,
                platform VARCHAR NOT NULL,
                server VARCHAR NOT NULL,
                platform_login VARCHAR NOT NULL,
                platform_password VARCHAR NOT NULL,
                account_size VARCHAR NOT NULL,
                account_type VARCHAR NOT NULL DEFAULT 'phase1',
                is_assigned BOOLEAN NOT NULL DEFAULT FALSE,
                order_id INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE,
                status VARCHAR NOT NULL DEFAULT 'pending'
            );
        """))
        
        conn.commit()
        print("account_credentials table created successfully.")
    else:
        print("account_credentials table already exists.")
        
        # Check if all required columns exist
        columns_to_check = [
            "platform", "server", "platform_login", "platform_password", 
            "account_size", "account_type", "is_assigned", "order_id",
            "created_at", "updated_at", "status"
        ]
        
        missing_columns = []
        
        for column in columns_to_check:
            result = conn.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'account_credentials' AND column_name = '{column}'
                );
            """))
            
            if not result.scalar():
                missing_columns.append(column)
        
        if missing_columns:
            print(f"Missing columns in account_credentials table: {', '.join(missing_columns)}")
            
            # Add missing columns
            for column in missing_columns:
                print(f"Adding column {column} to account_credentials table...")
                
                # Define column type based on column name
                if column == "is_assigned":
                    column_type = "BOOLEAN NOT NULL DEFAULT FALSE"
                elif column == "order_id":
                    column_type = "INTEGER"
                elif column in ["created_at", "updated_at"]:
                    column_type = "TIMESTAMP WITH TIME ZONE"
                    if column == "created_at":
                        column_type += " DEFAULT CURRENT_TIMESTAMP"
                elif column == "status":
                    column_type = "VARCHAR NOT NULL DEFAULT 'pending'"
                elif column == "account_type":
                    column_type = "VARCHAR NOT NULL DEFAULT 'phase1'"
                else:
                    column_type = "VARCHAR NOT NULL"
                
                # Add the column
                conn.execute(text(f"""
                    ALTER TABLE account_credentials
                    ADD COLUMN {column} {column_type};
                """))
                
                conn.commit()
                print(f"Column {column} added successfully.")
        else:
            print("All required columns exist in account_credentials table.")

print("Script completed successfully.")
