import os
from dotenv import load_dotenv
from sqlmodel import SQLModel, create_engine, Session
from datetime import datetime
import random
import sys

# Add the current directory to the Python path
sys.path.append('.')

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")
if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Import the CompleteOrderModel
try:
    from models.order import CompleteOrderModel
    print("Successfully imported CompleteOrderModel")
except ImportError as e:
    print(f"Error importing CompleteOrderModel: {str(e)}")
    exit(1)

# Create the SQLAlchemy engine
engine = create_engine(database_url, echo=True)

try:
    # Generate a unique order_id for testing
    test_order_id = random.randint(10000000, 99999999)
    
    # Create a test record
    complete_order = CompleteOrderModel(
        order_id=test_order_id,
        server="Test Server",
        platform_login="test_login",
        platform_password="test_password",
        session_id=None,  # Explicitly set to None
        terminal_id=None,  # Explicitly set to None
        profit_target=None,
        completed_at=datetime.utcnow()
    )
    
    print(f"Created CompleteOrderModel instance with order_id: {test_order_id}")
    
    # Save the record to the database
    with Session(engine) as session:
        print("Adding record to session...")
        session.add(complete_order)
        print("Committing session...")
        session.commit()
        print("Session committed successfully")
        
        # Refresh the instance to get the generated ID
        session.refresh(complete_order)
        print(f"Record inserted successfully with ID: {complete_order.id}")
    
    print("Test completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
