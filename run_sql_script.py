import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL not found in .env file, checking environment variables...")
    database_url = os.environ.get("DATABASE_URL")
    
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database...")

try:
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    print("Database connection established successfully.")
    
    # Read the SQL script
    with open('add_columns.sql', 'r') as f:
        sql_script = f.read()
    
    # Execute the SQL script
    print("Executing SQL script...")
    cursor.execute(sql_script)
    print("SQL script executed successfully.")
    
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'conn' in locals():
        conn.close()
    print("Database connection closed.")
