from fastapi import APIRouter
from fastapi.responses import HTMLResponse
from templates.email_templates import (
    create_welcome_email,
    create_order_confirmation_email,
    create_challenge_completion_email,
    create_pass_notification_email,
    create_fail_notification_email,
    create_live_account_email,
    create_certificate_email,
    create_referral_bonus_email,
    create_points_milestone_email
)

email_preview_router = APIRouter(prefix="/email-preview", tags=["Email Preview"])

@email_preview_router.get("/welcome", response_class=HTMLResponse)
def preview_welcome_email():
    """Preview the welcome email template"""
    return create_welcome_email("<PERSON> Doe", "ABC12345")

@email_preview_router.get("/verification", response_class=HTMLResponse)
def preview_verification_email():
    """Preview the email verification template"""
    from templates.email_templates import create_base_email_template
    
    content = """
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #4299E1 0%, #3182CE 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🔐</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Verify Your Email Address</h3>
    </div>
    
    <p style="margin: 0 0 30px; font-size: 16px; color: #2D3748; text-align: center;">We're excited to have you join FundedWhales! Please verify your email address to complete your registration.</p>
    
    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 3px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -20px; right: -20px; width: 40px; height: 40px; background: rgba(66, 153, 225, 0.2); border-radius: 50%;"></div>
        <div style="position: absolute; bottom: -15px; left: -15px; width: 30px; height: 30px; background: rgba(66, 153, 225, 0.3); border-radius: 50%;"></div>
        
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">Your Verification Code</h4>
        <div style="background: #FFFFFF; border: 2px dashed #4299E1; border-radius: 12px; padding: 20px; margin: 20px 0; position: relative; z-index: 2;">
            <code style="font-size: 36px; font-weight: 800; color: #1A365D; letter-spacing: 8px; font-family: 'Courier New', monospace;">ABC123</code>
        </div>
        <p style="margin: 15px 0 0 0; color: #4A5568; font-size: 14px;">Enter this code in the verification form</p>
    </div>
    
    <div style="background: #FFF5F5; border: 1px solid #FEB2B2; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <div style="display: flex; align-items: center;">
            <div style="background: #E53E3E; color: white; border-radius: 50%; width: 24px; height: 24px; line-height: 24px; text-align: center; margin-right: 12px; font-size: 14px;">⚠</div>
            <div>
                <p style="margin: 0; color: #C53030; font-weight: 600; font-size: 14px;">Security Notice</p>
                <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 13px;">This code expires in 24 hours. Never share it with anyone.</p>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0; color: #4A5568; font-size: 14px;">Having trouble? Contact our support team for assistance.</p>
    </div>
    """
    
    return create_base_email_template(
        "Verify Your Email Address",
        content,
        None,
        None,
        "#4299E1"
    )

@email_preview_router.get("/order-confirmation", response_class=HTMLResponse)
def preview_order_confirmation_email():
    """Preview the order confirmation email template"""
    return create_order_confirmation_email("John Doe", 12345, "Phase 1 Challenge", "$50,000", "MetaTrader 5")

@email_preview_router.get("/challenge-completion", response_class=HTMLResponse)
def preview_challenge_completion_email():
    """Preview the challenge completion email template"""
    return create_challenge_completion_email("John Doe", 12345, "mt5.server.com", "12345678", "SecurePass123")

@email_preview_router.get("/challenge-passed", response_class=HTMLResponse)
def preview_challenge_passed_email():
    """Preview the challenge passed email template"""
    return create_pass_notification_email("John Doe", 12345, 5250.75)

@email_preview_router.get("/challenge-failed", response_class=HTMLResponse)
def preview_challenge_failed_email():
    """Preview the challenge failed email template"""
    return create_fail_notification_email("John Doe", 12345, "Maximum daily loss limit exceeded. The account reached -5% daily drawdown on Day 3 of trading.")

@email_preview_router.get("/live-account", response_class=HTMLResponse)
def preview_live_account_email():
    """Preview the live account email template"""
    return create_live_account_email("John Doe", 12345, "live.server.com", "********", "LivePass456", "80%")

@email_preview_router.get("/referral-bonus", response_class=HTMLResponse)
def preview_referral_bonus_email():
    """Preview the referral bonus email template"""
    return create_referral_bonus_email("John Doe", "Jane Smith", "<EMAIL>", 50)

@email_preview_router.get("/points-milestone", response_class=HTMLResponse)
def preview_points_milestone_email():
    """Preview the points milestone email template"""
    return create_points_milestone_email("John Doe", 250, 250)

@email_preview_router.get("/certificate", response_class=HTMLResponse)
def preview_certificate_email():
    """Preview the certificate email template"""
    return create_certificate_email("John Doe", 12345, "Phase 1 Challenge", "$50,000", "CERT-2025-001", "2025-01-15", 5250.75)

@email_preview_router.get("/", response_class=HTMLResponse)
def email_preview_index():
    """Email preview index page"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Email Template Previews - FundedWhales</title>
        <style>
            body { font-family: 'Inter', sans-serif; margin: 40px; background: #f8fafc; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
            h1 { color: #1a365d; margin-bottom: 30px; }
            .email-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .email-card { border: 2px solid #e2e8f0; border-radius: 8px; padding: 20px; transition: all 0.3s ease; }
            .email-card:hover { border-color: #ff6b35; transform: translateY(-2px); box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1); }
            .email-card h3 { color: #1a365d; margin: 0 0 10px 0; }
            .email-card p { color: #4a5568; margin: 0 0 15px 0; font-size: 14px; }
            .email-card a { background: linear-gradient(135deg, #ff6b35 0%, #e53e3e 100%); color: white; text-decoration: none; padding: 10px 20px; border-radius: 6px; font-weight: 600; display: inline-block; }
            .email-card a:hover { transform: translateY(-1px); }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📧 Email Template Previews</h1>
            <p style="color: #4a5568; margin-bottom: 30px;">Preview all email templates used in the FundedWhales platform.</p>
            
            <div class="email-grid">
                <div class="email-card">
                    <h3>🎉 Welcome Email</h3>
                    <p>Sent to new users after successful registration with referral code display.</p>
                    <a href="/email-preview/welcome" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🔐 Email Verification</h3>
                    <p>Sent to users for email address verification with security code.</p>
                    <a href="/email-preview/verification" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>✅ Order Confirmation</h3>
                    <p>Sent after successful trading challenge order placement.</p>
                    <a href="/email-preview/order-confirmation" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🚀 Challenge Completion</h3>
                    <p>Sent when trading account is set up with login credentials.</p>
                    <a href="/email-preview/challenge-completion" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🏆 Challenge Passed</h3>
                    <p>Congratulatory email when user successfully passes their challenge.</p>
                    <a href="/email-preview/challenge-passed" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>📊 Challenge Failed</h3>
                    <p>Encouraging email when challenge is not passed with next steps.</p>
                    <a href="/email-preview/challenge-failed" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🌟 Live Account</h3>
                    <p>Sent when user gets their funded live trading account.</p>
                    <a href="/email-preview/live-account" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🎁 Referral Bonus</h3>
                    <p>Sent when user earns points from successful referrals.</p>
                    <a href="/email-preview/referral-bonus" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>🏆 Points Milestone</h3>
                    <p>Sent when user reaches significant points milestones.</p>
                    <a href="/email-preview/points-milestone" target="_blank">Preview</a>
                </div>
                
                <div class="email-card">
                    <h3>📜 Certificate</h3>
                    <p>Professional certificate email for completed challenges.</p>
                    <a href="/email-preview/certificate" target="_blank">Preview</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
