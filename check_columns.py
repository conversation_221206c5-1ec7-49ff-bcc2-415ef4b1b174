import os
from sqlmodel import SQLModel, create_engine, Session, select, inspect
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL not found in .env file, checking environment variables...")
    database_url = os.environ.get("DATABASE_URL")

    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database...")

# Create engine
engine = create_engine(database_url)

# List of tables and columns to check
columns_to_check = [
    {"table": "ordermodel", "column": "created_at"},
    {"table": "completeordermodel", "column": "completed_at"},
    {"table": "failorder", "column": "failed_at"},
    {"table": "rejectorder", "column": "rejected_at"},
    {"table": "passorder", "column": "pass_date"},
    {"table": "stage2account", "column": "created_at"},
    {"table": "liveaccount", "column": "created_at"},
    {"table": "ordertimeline", "column": "event_date"}
]

try:
    # Get inspector
    inspector = inspect(engine)

    # Check each column
    for column_info in columns_to_check:
        table = column_info["table"]
        column = column_info["column"]

        # Get columns for the table
        columns = inspector.get_columns(table)

        # Check if the column exists
        column_exists = False
        for col in columns:
            if col['name'] == column:
                column_exists = True
                print(f"Column '{column}' exists in table '{table}' with type '{col['type']}'.")
                break

        if not column_exists:
            print(f"Column '{column}' does not exist in table '{table}'.")

    print("All columns checked.")
except Exception as e:
    print(f"Error: {str(e)}")
    raise
