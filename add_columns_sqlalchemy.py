import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL not found in .env file, checking environment variables...")
    database_url = os.environ.get("DATABASE_URL")
    
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database...")

# Create SQLAlchemy engine
engine = create_engine(database_url)
print("Database connection established successfully.")

# List of tables and columns to add
columns_to_add = [
    {"table": "ordermodel", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "completeordermodel", "column": "completed_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "failorder", "column": "failed_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "rejectorder", "column": "rejected_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "passorder", "column": "pass_date", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "stage2account", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "liveaccount", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "ordertimeline", "column": "event_date", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
]

try:
    # Add each column if it doesn't exist
    with engine.connect() as connection:
        for column_info in columns_to_add:
            table = column_info["table"]
            column = column_info["column"]
            column_type = column_info["type"]
            default = column_info["default"]
            
            # Check if the column already exists
            check_query = text(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}';
            """)
            
            result = connection.execute(check_query)
            if not result.fetchone():
                print(f"Column '{column}' does not exist in table '{table}'. Adding it...")
                
                # Add the column with a default value
                add_column_query = text(f"""
                    ALTER TABLE {table} 
                    ADD COLUMN {column} {column_type} DEFAULT {default};
                """)
                
                connection.execute(add_column_query)
                connection.commit()
                
                print(f"Column '{column}' added successfully to table '{table}'.")
            else:
                print(f"Column '{column}' already exists in table '{table}'. No changes needed.")
    
    print("All columns added successfully.")
except Exception as e:
    print(f"Error: {str(e)}")
    
print("Database operations completed.")
