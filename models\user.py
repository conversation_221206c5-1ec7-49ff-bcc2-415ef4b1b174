from sqlmodel import SQLModel, <PERSON>
from datetime import datetime
from typing import Optional  # Keep the import for Optional
from pydantic import EmailStr
import secrets
import string

def generate_referral_code() -> str:
    """Generate a unique 8-character referral code"""
    characters = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(characters) for _ in range(8))

class User(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    username: str
    email: EmailStr
    hashed_password: str
    name: str
    phone_no: str
    country: str
    address: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Referral system fields
    referral_code: str = Field(default_factory=generate_referral_code, unique=True, index=True)
    referred_by: Optional[int] = Field(default=None, foreign_key="user.id")
    points_balance: int = Field(default=0)
     