import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

print(f"Environment variables loaded: {os.environ.keys()}")
print(f"DATABASE_URL exists: {database_url is not None}")

if not database_url:
    # Try to get the database URL from the environment directly
    database_url = os.environ.get("DATABASE_URL")
    print(f"Trying to get DATABASE_URL directly: {database_url}")

    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database: {database_url}")

# Connect to the database
try:
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    print("Database connection established successfully.")
except Exception as e:
    print(f"Error connecting to database: {str(e)}")
    raise

try:
    # Check if the column already exists
    cursor.execute("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name='ordermodel' AND column_name='created_at';
    """)

    if cursor.fetchone() is None:
        print("Column 'created_at' does not exist. Adding it...")

        # Add the created_at column with a default value of current timestamp
        cursor.execute("""
            ALTER TABLE ordermodel
            ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        """)

        print("Column 'created_at' added successfully.")
    else:
        print("Column 'created_at' already exists. No changes needed.")

except Exception as e:
    print(f"Error: {str(e)}")
finally:
    cursor.close()
    conn.close()
    print("Database connection closed.")
