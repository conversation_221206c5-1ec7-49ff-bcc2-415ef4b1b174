import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL not found in .env file, checking environment variables...")
    database_url = os.environ.get("DATABASE_URL")
    
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is not set")

print(f"Connecting to database...")

# Connect to the database
try:
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    print("Database connection established successfully.")
except Exception as e:
    print(f"Error connecting to database: {str(e)}")
    raise

# List of tables and columns to add
columns_to_add = [
    {"table": "ordermodel", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "completeordermodel", "column": "completed_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "failorder", "column": "failed_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "rejectorder", "column": "rejected_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "passorder", "column": "pass_date", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "stage2account", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "liveaccount", "column": "created_at", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"},
    {"table": "ordertimeline", "column": "event_date", "type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
]

try:
    # Add each column if it doesn't exist
    for column_info in columns_to_add:
        table = column_info["table"]
        column = column_info["column"]
        column_type = column_info["type"]
        default = column_info["default"]
        
        # Check if the column already exists
        cursor.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='{table}' AND column_name='{column}';
        """)
        
        if cursor.fetchone() is None:
            print(f"Column '{column}' does not exist in table '{table}'. Adding it...")
            
            # Add the column with a default value
            cursor.execute(f"""
                ALTER TABLE {table} 
                ADD COLUMN {column} {column_type} DEFAULT {default};
            """)
            
            print(f"Column '{column}' added successfully to table '{table}'.")
        else:
            print(f"Column '{column}' already exists in table '{table}'. No changes needed.")
    
    print("All columns added successfully.")
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    cursor.close()
    conn.close()
    print("Database connection closed.")
